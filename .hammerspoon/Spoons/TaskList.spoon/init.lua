--- === TaskList ===
---
--- 多任务 Hammerspoon menubar 管理器
--- 提供任务管理、倒计时、数据持久化等功能
---
--- Download: [https://github.com/your-repo/TaskList.spoon](https://github.com/your-repo/TaskList.spoon)

local obj = {}
obj.__index = obj

-- Metadata
obj.name = "TaskList"
obj.version = "1.0.0"
obj.author = "<EMAIL>"
obj.homepage = "http://lucc.dev/"
obj.license = "MIT - https://opensource.org/licenses/MIT"

obj.logger = hs.logger.new('TaskList')

-- 加载模块
local spoonPath = hs.configdir .. "/Spoons/TaskList.spoon"
local cronTasks = dofile(spoonPath .. "/cron_tasks.lua")
local taskManager = dofile(spoonPath .. "/task_manager.lua")
local dataManager = dofile(spoonPath .. "/data_manager.lua")

-- 多任务 Hammerspoon menubar 管理器
local menubar = nil  -- 初始化为 nil，在 start() 中创建
local tasks = {}  -- 存储所有任务
local currentTaskId = nil  -- 当前选中的任务ID
local maxTasks = 20  -- 最大任务数量

local countdownTimer = nil  -- 倒计时计时器
local remainingSeconds = 0  -- 剩余秒数
local isPaused = false      -- 是否暂停
local taskCountdowns = {}   -- 存储每个任务的剩余倒计时时间（按任务ID存储）

-- UTF-8 字符串处理函数
--返回截取的实际Index
function SubStringGetTrueIndex(str, index)
    local curIndex = 0
    local i = 1
    local lastCount = 1
    repeat
        lastCount = SubStringGetByteCount(str, i)
        i = i + lastCount
        curIndex = curIndex + 1
    until (curIndex >= index)
    return i - lastCount
end

--返回当前字符实际占用的字符数
function SubStringGetByteCount(str, index)
    local curByte = string.byte(str, index)
    local byteCount = 1
    if curByte == nil then
        byteCount = 0
    elseif curByte > 0 and curByte <= 127 then
        byteCount = 1
    elseif curByte >= 192 and curByte <= 223 then
        byteCount = 2
    elseif curByte >= 224 and curByte <= 239 then
        byteCount = 3
    elseif curByte >= 240 and curByte <= 247 then
        byteCount = 4
    end
    return byteCount
end

--截取中英混合的字符串
function SubString(str, startIndex, endIndex)
    if type(str) ~= "string" then
        return
    end
    if startIndex == nil or startIndex < 0 then
        return
    end

    if endIndex == nil or endIndex < 0 then
        return
    end

    return string.sub(str, SubStringGetTrueIndex(str, startIndex),
            SubStringGetTrueIndex(str, endIndex + 1) - 1)
end

-- 计算评分 (1-5分，整数制)
local function calculateScore(task)
    if not task.isDone or task.estimatedTime == 0 then
        return 0
    end

    local timeRatio = task.actualTime / (task.estimatedTime * 40) -- 40分钟为一个单位
    local baseScore = 3 -- 基础分数

    -- 1. 时间效率评分 (权重: 60%)
    local timeScore = 0
    if timeRatio <= 0.5 then
        timeScore = 2  -- 极快完成 (5分)
    elseif timeRatio <= 0.8 then
        timeScore = 1  -- 提前完成 (4分)
    elseif timeRatio <= 1.0 then
        timeScore = 0  -- 按时完成 (3分)
    elseif timeRatio <= 1.2 then
        timeScore = -1 -- 轻微超时 (2分)
    elseif timeRatio <= 1.5 then
        timeScore = -2 -- 中度超时 (1分)
    elseif timeRatio <= 2.0 then
        timeScore = -2 -- 严重超时 (1分)
    else
        timeScore = -2 -- 极度超时 (1分)
    end

    -- 2. 任务复杂度评分 (权重: 20%)
    local complexityBonus = 0
    if task.estimatedTime >= 3 then
        complexityBonus = 1  -- 复杂任务完成加分
    elseif task.estimatedTime >= 2 then
        complexityBonus = 0  -- 中等任务无加分
    else
        complexityBonus = 0  -- 简单任务无加分
    end

    -- 3. 完成时间评分 (权重: 10%)
    local timeOfDayBonus = 0
    if task.doneAt then
        local hour = tonumber(task.doneAt:match("(%d%d):"))
        if hour and hour >= 9 and hour <= 18 then
            timeOfDayBonus = 0  -- 工作时间完成，无额外加分
        elseif hour and (hour >= 19 and hour <= 22) then
            timeOfDayBonus = 0  -- 晚上完成，无额外加分
        else
            timeOfDayBonus = -1 -- 深夜或早晨完成，扣分
        end
    end

    -- 4. 任务及时性评分 (权重: 10%)
    local timelinessBonus = 0
    if task.date and task.doneAt then
        local taskDate = task.date
        local doneDate = task.doneAt:match("^(%d%d%d%d%-%d%d%-%d%d)")
        if doneDate == taskDate then
            timelinessBonus = 0  -- 当天完成，无额外加分
        elseif doneDate > taskDate then
            timelinessBonus = -1 -- 延期完成，扣分
        else
            timelinessBonus = 1  -- 提前完成，加分
        end
    end

    -- 综合计算最终分数
    local finalScore = baseScore + timeScore + complexityBonus + timeOfDayBonus + timelinessBonus

    -- 确保分数在1-5范围内
    finalScore = math.max(1, math.min(5, finalScore))

    -- 返回整数分数
    return math.floor(finalScore + 0.5)
end

-- 前置声明函数，解决函数调用顺序问题
local updateMenubar
local startCountdown
local stopCountdown
local toggleCountdown
local calculateCountdownTime

-- 安全的通知发送函数
local function sendNotification(title, text, withdrawAfter, soundName)
    withdrawAfter = withdrawAfter or 3

    local notification = hs.notify.new({
        title = title or "任务管理器",
        informativeText = text or "",
        withdrawAfter = withdrawAfter
    })

    if soundName then
        notification:soundName(soundName)
    end

    local success = pcall(function()
        notification:send()
    end)

    if not success then
        print("通知发送失败: " .. title .. " - " .. text)
        -- 如果通知失败，至少在控制台输出
        print("📢 " .. title .. ": " .. text)
    end

    return success
end

-- 加载任务数据
local function loadTasks()
    local needSave
    tasks, currentTaskId, needSave = dataManager.loadTasks(taskManager, cronTasks, obj.logger)
    
    if needSave then
        dataManager.saveTasks(tasks, currentTaskId)
    end
end

-- 保存任务数据
local function saveTasks()
    dataManager.saveTasks(tasks, currentTaskId)
end

-- 更新菜单栏显示
updateMenubar = function()
    if menubar then
        local displayText = "无任务"

        local currentTask = taskManager.findTaskById(tasks, currentTaskId)
        if currentTask and not currentTask.isDone then
            local taskName = currentTask.name

            -- 使用 UTF-8 安全的字符串截取
            local maxLength = 20
            local taskNameLength = 0
            local i = 1
            while i <= string.len(taskName) do
                local byteCount = SubStringGetByteCount(taskName, i)
                if taskNameLength >= maxLength then
                    taskName = SubString(taskName, 1, maxLength - 3) .. "..."
                    break
                end
                taskNameLength = taskNameLength + 1
                i = i + byteCount
            end

            -- 如果有倒计时，显示倒计时
            if remainingSeconds > 0 then
                local minutes = math.floor(remainingSeconds / 60)
                local seconds = remainingSeconds % 60
                local timeStr = string.format("%d:%02d", minutes, seconds)
                local pauseIcon = isPaused and "⏸" or "⏱"
                displayText = pauseIcon .. " " .. timeStr .. " | " .. taskName
            else
                displayText = taskName
            end
        end

        -- 使用更小的字体
        local styledText = hs.styledtext.new(displayText, {
            font = { name = "Helvetica", size = 12 }
        })
        menubar:setTitle(styledText)
    end
end

-- 添加新任务（分步对话框）
local function addTask()
    local activeTasks = taskManager.getActiveTasks(tasks)
    if #activeTasks >= maxTasks then
        hs.notify.new({
            title = "任务管理器",
            informativeText = "活跃任务数量已达上限 (" .. maxTasks .. ")",
            withdrawAfter = 5
        }):send()
        return
    end

    -- 第一步：获取任务名称
    local button, taskName = hs.dialog.textPrompt(
            "添加新任务 - 步骤 1/3",
            "请输入任务名称:",
            "",
            "下一步",
            "取消"
    )
    if button ~= "下一步" or not taskName or taskName == "" then
        return
    end

    -- 第二步：获取日期
    local button2, dateStr = hs.dialog.textPrompt(
            "添加新任务 - 步骤 2/3",
            "请输入日期 (格式: YYYY-MM-DD):",
            taskManager.getCurrentDate(),
            "下一步",
            "取消"
    )
    if button2 ~= "下一步" then
        return
    end

    if not taskManager.isValidDate(dateStr) then
        hs.notify.new({
            title = "输入错误",
            informativeText = "日期格式错误，请使用 YYYY-MM-DD 格式",
            withdrawAfter = 5
        }):send()
        return
    end

    -- 第三步：获取预计耗时
    local button3, estimatedStr = hs.dialog.textPrompt(
            "添加新任务 - 步骤 3/3",
            "请输入预计耗时 (几个E1f，每个E1f=40分钟):",
            "1",
            "完成",
            "取消"
    )
    if button3 ~= "完成" then
        return
    end

    local estimatedTime = tonumber(estimatedStr) or 1
    if estimatedTime < 1 then
        estimatedTime = 1
    end

    -- 创建新任务
    local addTime = math.floor(hs.timer.secondsSinceEpoch() * 1000) -- 任务添加时间（精确到毫秒）
    local newTask = {
        id = taskManager.generateTaskId(addTime, taskName, dateStr, estimatedTime),
        name = taskName,
        date = dateStr,
        addTime = addTime,
        estimatedTime = estimatedTime,
        actualTime = 0,
        isDone = false,
        doneAt = nil,
        startTime = nil
    }

    table.insert(tasks, newTask)
    taskManager.sortTasks(tasks)

    updateMenubar()
    saveTasks()

    hs.notify.new({
        title = "任务管理器",
        informativeText = "任务已添加: " .. taskName,
        withdrawAfter = 3
    }):send()
end

-- 创建菜单项
local function createMenu()
    local menu = {}
    local activeTasks = taskManager.getActiveTasks(tasks)

    -- 当前任务显示
    local currentTask = taskManager.findTaskById(tasks, currentTaskId)
    if currentTask and not currentTask.isDone then
        table.insert(menu, {
            title = "当前: " .. currentTask.name,
            disabled = true
        })

        table.insert(menu, { title = "-" })
    end

    -- 按日期分组显示活跃任务
    if #activeTasks > 0 then
        table.insert(menu, {
            title = "活跃任务 (" .. #activeTasks .. "/" .. maxTasks .. ")",
            disabled = true
        })

        -- 按日期分组任务
        local tasksByDate = {}
        for _, activeTask in ipairs(activeTasks) do
            local task = activeTask.task
            local date = task.date
            if not tasksByDate[date] then
                tasksByDate[date] = {}
            end
            table.insert(tasksByDate[date], activeTask)
        end

        -- 获取所有日期并排序
        local dates = {}
        for date, _ in pairs(tasksByDate) do
            table.insert(dates, date)
        end
        table.sort(dates)

        -- 按日期显示任务
        for _, date in ipairs(dates) do
            local dateTitle
            if date == taskManager.getCurrentDate() then
                dateTitle = "📅 今天"
            else
                dateTitle = "📅 " .. date
            end

            table.insert(menu, {
                title = dateTitle,
                disabled = true
            })

            for _, activeTask in ipairs(tasksByDate[date]) do
                local task = activeTask.task
                local index = activeTask.index
                local prefix = (task.id == currentTaskId) and "● " or "○ "

                -- 使用 UTF-8 安全的字符串截取
                local maxLength = 50
                local displayTask = task.name
                local taskNameLength = 0
                local i = 1
                while i <= string.len(displayTask) do
                    local byteCount = SubStringGetByteCount(displayTask, i)
                    if taskNameLength >= maxLength then
                        displayTask = SubString(displayTask, 1, maxLength - 3) .. "..."
                        break
                    end
                    taskNameLength = taskNameLength + 1
                    i = i + byteCount
                end

                local taskTitle = "  " .. prefix .. displayTask

                table.insert(menu, {
                    title = taskTitle,
                    menu = {
                        {
                            title = "选为当前任务",
                            fn = function() print("Select task functionality not implemented") end
                        },
                        {
                            title = "编辑任务",
                            fn = function() print("Edit task functionality not implemented") end
                        },
                        {
                            title = "完成任务",
                            fn = function() print("Complete task functionality not implemented") end
                        },
                        {
                            title = "删除任务",
                            fn = function() print("Delete task functionality not implemented") end
                        }
                    }
                })
            end
        end
        table.insert(menu, { title = "-" })
    end

    -- 操作选项
    table.insert(menu, {
        title = "➕ 添加新任务",
        fn = addTask
    })

    table.insert(menu, { title = "-" })

    -- 显示已完成任务数量
    local completedCount = 0
    for _, task in ipairs(tasks) do
        if task.isDone then
            completedCount = completedCount + 1
        end
    end

    if completedCount > 0 then
        table.insert(menu, {
            title = "已完成任务: " .. completedCount,
            disabled = true
        })
        table.insert(menu, { title = "-" })
    end

    table.insert(menu, {
        title = "退出",
        fn = function()
            menubar:delete()
            menubar = nil
        end
    })

    return menu
end

--- TaskList:start()
--- Method
--- 启动 TaskList
---
--- Parameters:
---  * None
---
--- Returns:
---  * The TaskList object
function obj:start()
    -- 创建 menubar
    menubar = hs.menubar.new()

    -- 设置菜单
    menubar:setMenu(createMenu)

    -- 点击菜单栏图标时快速添加任务
    menubar:setClickCallback(function()
        addTask()
    end)

    -- 初始化
    loadTasks()
    taskManager.sortTasks(tasks)
    updateMenubar()

    obj.logger.i("TaskList started with " .. #tasks .. " tasks")
    return self
end

--- TaskList:stop()
--- Method
--- 停止 TaskList
---
--- Parameters:
---  * None
---
--- Returns:
---  * The TaskList object
function obj:stop()
    if menubar then
        menubar:delete()
        menubar = nil
    end

    if countdownTimer then
        countdownTimer:stop()
        countdownTimer = nil
    end

    if obj.updateTimer then
        obj.updateTimer:stop()
        obj.updateTimer = nil
    end

    -- 保存数据
    saveTasks()

    obj.logger.i("TaskList stopped")
    return self
end

return obj
